#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/cauchy_ops.h>

namespace at {


// aten::cauchy.out(Tensor self, float median=0, float sigma=1, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & cauchy_out(at::Tensor & out, const at::Tensor & self, double median=0, double sigma=1, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::cauchy_out::call(self, median, sigma, generator, out);
}
// aten::cauchy.out(Tensor self, float median=0, float sigma=1, *, Generator? generator=None, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & cauchy_outf(const at::Tensor & self, double median, double sigma, ::std::optional<at::Generator> generator, at::Tensor & out) {
    return at::_ops::cauchy_out::call(self, median, sigma, generator, out);
}

// aten::cauchy(Tensor self, float median=0, float sigma=1, *, Generator? generator=None) -> Tensor
inline at::Tensor cauchy(const at::Tensor & self, double median=0, double sigma=1, ::std::optional<at::Generator> generator=::std::nullopt) {
    return at::_ops::cauchy::call(self, median, sigma, generator);
}

}

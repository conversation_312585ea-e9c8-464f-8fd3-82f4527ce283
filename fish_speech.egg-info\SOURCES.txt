.dockerignore
.gitignore
.pre-commit-config.yaml
.project-root
.readthedocs.yaml
API_FLAGS.txt
LICENSE
README.md
docker-compose.dev.yml
dockerfile
dockerfile.dev
entrypoint.sh
inference.ipynb
mkdocs.yml
pyproject.toml
pyrightconfig.json
uv.lock
.github/pull_request_template.md
.github/ISSUE_TEMPLATE/bug_report.yml
.github/ISSUE_TEMPLATE/config.yml
.github/ISSUE_TEMPLATE/feature_request.yml
.github/workflows/build-docker-image.yml
.github/workflows/docs.yml
.github/workflows/stale.yml
docs/CNAME
docs/README.ja.md
docs/README.ko.md
docs/README.pt-BR.md
docs/README.zh.md
docs/requirements.txt
docs/assets/Elo.jpg
docs/assets/Thumbnail.jpg
docs/assets/openaudio.jpg
docs/assets/openaudio.png
docs/en/index.md
docs/en/inference.md
docs/en/install.md
docs/ja/index.md
docs/ja/inference.md
docs/ja/install.md
docs/ko/index.md
docs/ko/inference.md
docs/ko/install.md
docs/pt/index.md
docs/pt/inference.md
docs/pt/install.md
docs/stylesheets/extra.css
docs/zh/index.md
docs/zh/inference.md
docs/zh/install.md
fish_speech/content_sequence.py
fish_speech/tokenizer.py
fish_speech.egg-info/PKG-INFO
fish_speech.egg-info/SOURCES.txt
fish_speech.egg-info/dependency_links.txt
fish_speech.egg-info/requires.txt
fish_speech.egg-info/top_level.txt
fish_speech/configs/base.yaml
fish_speech/configs/modded_dac_vq.yaml
fish_speech/configs/text2semantic_finetune.yaml
fish_speech/configs/lora/r_8_alpha_16.yaml
fish_speech/i18n/README.md
fish_speech/i18n/__init__.py
fish_speech/i18n/core.py
fish_speech/i18n/scan.py
fish_speech/i18n/locale/en_US.json
fish_speech/i18n/locale/es_ES.json
fish_speech/i18n/locale/ja_JP.json
fish_speech/i18n/locale/ko_KR.json
fish_speech/i18n/locale/pt_BR.json
fish_speech/i18n/locale/zh_CN.json
fish_speech/inference_engine/__init__.py
fish_speech/inference_engine/reference_loader.py
fish_speech/inference_engine/utils.py
fish_speech/inference_engine/vq_manager.py
fish_speech/models/dac/__init__.py
fish_speech/models/dac/inference.py
fish_speech/models/dac/modded_dac.py
fish_speech/models/dac/rvq.py
fish_speech/models/text2semantic/__init__.py
fish_speech/models/text2semantic/inference.py
fish_speech/models/text2semantic/llama.py
fish_speech/models/text2semantic/lora.py
fish_speech/text/__init__.py
fish_speech/text/clean.py
fish_speech/utils/__init__.py
fish_speech/utils/braceexpand.py
fish_speech/utils/context.py
fish_speech/utils/file.py
fish_speech/utils/instantiators.py
fish_speech/utils/logger.py
fish_speech/utils/logging_utils.py
fish_speech/utils/rich_utils.py
fish_speech/utils/schema.py
fish_speech/utils/spectrogram.py
fish_speech/utils/utils.py
tools/api_client.py
tools/api_server.py
tools/download_models.py
tools/run_webui.py
tools/llama/quantize.py
tools/server/api_utils.py
tools/server/exception_handler.py
tools/server/inference.py
tools/server/model_manager.py
tools/server/model_utils.py
tools/server/views.py
tools/vqgan/create_train_split.py
tools/vqgan/extract_vq.py
tools/webui/__init__.py
tools/webui/inference.py
tools/webui/variables.py
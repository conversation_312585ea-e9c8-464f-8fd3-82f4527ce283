import os
import time
import pyfiglet

# 设置关键环境变量来修复 Gradio 问题
os.environ["GRADIO_SERVER_NAME"] = "0.0.0.0"  # 使用 0.0.0.0 而不是 127.0.0.1
os.environ["GRADIO_ANALYTICS_ENABLED"] = "False"
os.environ["GRADIO_DEBUG"] = "False"
os.environ["GRADIO_SHARE"] = "False"
os.environ["GRADIO_TEMP_DIR"] = os.path.join(os.getcwd(), "tmp")

# 禁用一些可能导致问题的功能
os.environ["GRADIO_FLAGGING_MODE"] = "never"
os.environ["GRADIO_ALLOW_FLAGGING"] = "never"

# 网络相关设置
os.environ["PYTHONHTTPSVERIFY"] = "0"  # 禁用 SSL 验证
os.environ["CURL_CA_BUNDLE"] = ""      # 清空 CA bundle

# 清屏（可选）
os.system('cls' if os.name == 'nt' else 'clear')

# 标题大字
big_text = pyfiglet.figlet_format("fish-speech50", font="slant")

# 构建展示块
banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
{big_text.rstrip()}
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║   📦 项目名称：fish-speech50                                                ║
║   🧠 功能定位：多语音识别 + 多模型支持 + 图形交互                          ║
║                                                                              ║
║   👑 当前版本：v5.0   ｜  构建环境：PyTorch 2.8.0 + CUDA 12.8               ║
║   📂 启动路径：{os.getcwd()}                                                 
║                                                                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║   🎬 油管：王知风    ｜  📺 B站：AI王知风                                    ║
║   💬 AI工具QQ2群：773608333                                                ║
║   🧾 官网：wangzhifeng.vip   ｜ 作者：王知风                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""

print()
print(banner)
print("\n" + "═" * 80 + "\n")
time.sleep(1.5)

from argparse import ArgumentParser
from pathlib import Path

import pyrootutils
import torch
from loguru import logger

pyrootutils.setup_root(__file__, indicator=".project-root", pythonpath=True)

from fish_speech.inference_engine import TTSInferenceEngine
from fish_speech.models.dac.inference import load_model as load_decoder_model
from fish_speech.models.text2semantic.inference import launch_thread_safe_queue
from fish_speech.utils.schema import ServeTTSRequest
from tools.webui import build_app
from tools.webui.inference import get_inference_wrapper

# Make einx happy
os.environ["EINX_FILTER_TRACEBACK"] = "false"


def parse_args():
    parser = ArgumentParser()
    parser.add_argument(
        "--llama-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini",
    )
    parser.add_argument(
        "--decoder-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini/codec.pth",
    )
    parser.add_argument("--decoder-config-name", type=str, default="modded_dac_vq")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--half", action="store_true")
    parser.add_argument("--compile", action="store_true")
    parser.add_argument("--max-gradio-length", type=int, default=0)
    parser.add_argument("--theme", type=str, default="light")

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    args.precision = torch.half if args.half else torch.bfloat16

    # Check if MPS or CUDA is available
    if torch.backends.mps.is_available():
        args.device = "mps"
        logger.info("mps is available, running on mps.")
    elif torch.xpu.is_available():
        args.device = "xpu"
        logger.info("XPU is available, running on XPU.")
    elif not torch.cuda.is_available():
        logger.info("CUDA is not available, running on CPU.")
        args.device = "cpu"

    logger.info("Loading Llama model...")
    llama_queue = launch_thread_safe_queue(
        checkpoint_path=args.llama_checkpoint_path,
        device=args.device,
        precision=args.precision,
        compile=args.compile,
    )

    logger.info("Loading VQ-GAN model...")
    decoder_model = load_decoder_model(
        config_name=args.decoder_config_name,
        checkpoint_path=args.decoder_checkpoint_path,
        device=args.device,
    )

    logger.info("Decoder model loaded, warming up...")

    # Create the inference engine
    inference_engine = TTSInferenceEngine(
        llama_queue=llama_queue,
        decoder_model=decoder_model,
        compile=args.compile,
        precision=args.precision,
    )

    # Dry run to check if the model is loaded correctly and avoid the first-time latency
    list(
        inference_engine.inference(
            ServeTTSRequest(
                text="Hello world.",
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=0.7,
                repetition_penalty=1.5,
                temperature=0.7,
                format="wav",
            )
        )
    )

    logger.info("Warming up done, launching the web UI...")

    # Get the inference function with the immutable arguments
    inference_fct = get_inference_wrapper(inference_engine)

    app = build_app(inference_fct, theme="dark")
    
    # 使用环境变量修复的启动配置
    logger.info("🌐 使用环境变量修复启动 Gradio Web UI...")
    logger.info("🔧 已设置以下修复:")
    logger.info("   - 服务器地址: 0.0.0.0 (而非 127.0.0.1)")
    logger.info("   - 禁用分析和调试")
    logger.info("   - 禁用 SSL 验证")
    logger.info("   - 设置临时目录")
    
    try:
        # 第一次尝试：使用 0.0.0.0 地址
        logger.info("🔄 尝试 1: 使用 0.0.0.0:7860")
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            inbrowser=True,
            show_api=True,
            quiet=False,
            show_error=True,
            debug=False,
            auth=None,
            auth_message=None,
            prevent_thread_lock=False,
            height=600,
            width=None,
            favicon_path=None,
            ssl_keyfile=None,
            ssl_certfile=None,
            ssl_keyfile_password=None,
            ssl_verify=False,  # 禁用 SSL 验证
            app_kwargs={}
        )
        
        logger.info("✅ Gradio Web UI 启动成功!")
        logger.info("🌐 请在浏览器中访问以下任一地址:")
        logger.info("   - http://localhost:7860")
        logger.info("   - http://127.0.0.1:7860")
        logger.info("   - http://0.0.0.0:7860")
        
    except Exception as e:
        logger.error(f"❌ 第一次尝试失败: {e}")
        logger.info("🔄 尝试 2: 使用共享模式")
        
        try:
            # 关闭之前的实例
            app.close()
        except:
            pass
        
        try:
            # 第二次尝试：共享模式
            app.launch(
                share=True,
                inbrowser=True,
                show_api=True,
                quiet=False,
                show_error=True,
                debug=False,
                prevent_thread_lock=False,
                ssl_verify=False
            )
            
            logger.info("✅ Gradio Web UI 通过共享模式启动成功!")
            logger.info("🌐 请使用上方显示的公共 URL 访问")
            
        except Exception as e2:
            logger.error(f"❌ 第二次尝试也失败: {e2}")
            logger.info("🔄 尝试 3: 最小配置模式")
            
            try:
                # 关闭之前的实例
                app.close()
            except:
                pass
            
            try:
                # 第三次尝试：最小配置
                app.launch(
                    server_name="localhost",
                    server_port=7861,
                    share=False,
                    inbrowser=False,
                    show_api=False,
                    quiet=True,
                    show_error=True,
                    debug=False,
                    ssl_verify=False
                )
                
                logger.info("✅ Gradio Web UI 以最小配置启动成功!")
                logger.info("🌐 请手动打开浏览器访问: http://localhost:7861")
                
            except Exception as e3:
                logger.error(f"❌ 所有尝试都失败了: {e3}")
                logger.info("")
                logger.info("🔧 建议的解决方案:")
                logger.info("1. 运行 '修复Gradio网络问题.bat' (需要管理员权限)")
                logger.info("2. 重启计算机")
                logger.info("3. 临时关闭防火墙和杀毒软件")
                logger.info("4. 检查是否有代理软件在运行")
                logger.info("5. 使用 API 服务器替代: 启动API服务器.bat")
                logger.info("")
                logger.info("💡 模型已成功加载，问题仅在于网络界面启动")
                
                input("按 Enter 键退出...")

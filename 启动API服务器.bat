@echo off
:: Fish Speech API Server Launcher - Alternative to Web UI

:: Virtual environment path
set PYTHON_ENV=%cd%\wzf

:: Main program entry (using API server)
set APP_ENTRY=.\tools\run_api_server.py

:: Gradio temp directory
set GRADIO_TEMP_DIR=%cd%\tmp

:: Clear interfering variables
set PYTHONHOME=
set PYTHONPATH=
set PYTHONEXECUTABLE=%PYTHON_ENV%\python.exe
set PYTHONWEXECUTABLE=%PYTHON_ENV%\pythonw.exe
set PYTHON_BIN_PATH=%PYTHONEXECUTABLE%
set PYTHON_LIB_PATH=%PYTHON_ENV%\Lib\site-packages

:: CUDA path
set CU_PATH=%PYTHON_ENV%\Lib\site-packages\torch\lib
set cuda_PATH=%PYTHON_ENV%\Library\bin

:: FFMPEG path
set FFMPEG_PATH=%PYTHON_ENV%\ffmpeg\bin

:: Update PATH
set PATH=%PYTHON_ENV%;%PYTHON_ENV%\Scripts;%FFMPEG_PATH%;%CU_PATH%;%cuda_PATH%;%PATH%

:: Check FFmpeg
echo Checking FFmpeg availability...
where ffmpeg

echo.
echo ========================================
echo Fish Speech API Server
echo ========================================
echo.
echo This is an alternative to the Web UI that uses a simple
echo HTTP server instead of Gradio. This should work even
echo when Gradio has network issues.
echo.
echo The server will start on: http://127.0.0.1:8080
echo.
echo Starting Fish Speech API Server...
echo.

"%PYTHONEXECUTABLE%" "%APP_ENTRY%" --host 127.0.0.1 --port 8080

echo.
echo Program ended, please check error messages above
pause

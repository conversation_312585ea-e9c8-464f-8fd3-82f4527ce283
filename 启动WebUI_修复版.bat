@echo off
:: Fish Speech Gradio Web UI Launcher - Fixed Version

:: Virtual environment path
set PYTHON_ENV=%cd%\wzf

:: Main program entry (using fixed version)
set APP_ENTRY=.\tools\run_webui_fixed.py

:: Gradio temp directory
set GRADIO_TEMP_DIR=%cd%\tmp

:: Clear interfering variables
set PYTHONHOME=
set PYTHONPATH=
set PYTHONEXECUTABLE=%PYTHON_ENV%\python.exe
set PYTHONWEXECUTABLE=%PYTHON_ENV%\pythonw.exe
set PYTHON_BIN_PATH=%PYTHONEXECUTABLE%
set PYTHON_LIB_PATH=%PYTHON_ENV%\Lib\site-packages

:: CUDA path
set CU_PATH=%PYTHON_ENV%\Lib\site-packages\torch\lib
set cuda_PATH=%PYTHON_ENV%\Library\bin

:: FFMPEG path
set FFMPEG_PATH=%PYTHON_ENV%\ffmpeg\bin

:: Update PATH
set PATH=%PYTHON_ENV%;%PYTHON_ENV%\Scripts;%FFMPEG_PATH%;%CU_PATH%;%cuda_PATH%;%PATH%

:: Set Gradio environment variables to fix issues
set GRADIO_SERVER_NAME=127.0.0.1
set GRADIO_ANALYTICS_ENABLED=False
set GRADIO_DEBUG=False
set GRADIO_TEMP_DIR=%cd%\tmp

:: Check FFmpeg
echo Checking FFmpeg availability...
where ffmpeg

echo.
echo ========================================
echo Fish Speech Gradio Web UI - Fixed
echo ========================================
echo.
echo This version includes multiple fixes for Gradio startup issues:
echo - Automatic localhost access repair
echo - Multiple launch configurations
echo - Automatic port detection
echo - Enhanced error handling
echo.
echo Starting Fish Speech Gradio Web UI...
echo.

"%PYTHONEXECUTABLE%" "%APP_ENTRY%"

echo.
echo Program ended, please check error messages above
pause

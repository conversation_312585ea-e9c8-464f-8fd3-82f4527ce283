#!/usr/bin/env python3
"""
Fish Speech API Server - Alternative to Web UI
This provides a simple HTTP API when Gradio Web UI fails to start
"""

import os
import time
import json
import pyfiglet
from pathlib import Path
from argparse import ArgumentParser
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading

import pyrootutils
import torch
from loguru import logger

pyrootutils.setup_root(__file__, indicator=".project-root", pythonpath=True)

from fish_speech.inference_engine import TTSInferenceEngine
from fish_speech.models.dac.inference import load_model as load_decoder_model
from fish_speech.models.text2semantic.inference import launch_thread_safe_queue
from fish_speech.utils.schema import ServeTTSRequest

# Make einx happy
os.environ["EINX_FILTER_TRACEBACK"] = "false"

# Global inference engine
inference_engine = None

class FishSpeechHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fish Speech API Server</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .form-group { margin: 20px 0; }
                    label { display: block; margin-bottom: 5px; font-weight: bold; }
                    input, textarea, button { width: 100%; padding: 10px; margin-bottom: 10px; }
                    button { background-color: #4CAF50; color: white; border: none; cursor: pointer; }
                    button:hover { background-color: #45a049; }
                    .result { margin-top: 20px; padding: 20px; background-color: #f0f0f0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🐟 Fish Speech API Server</h1>
                    <p>Web UI alternative - Generate speech using HTTP API</p>
                    
                    <form id="ttsForm">
                        <div class="form-group">
                            <label for="text">Text to synthesize:</label>
                            <textarea id="text" name="text" rows="4" placeholder="Enter text here...">Hello, this is a test of Fish Speech.</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="temperature">Temperature (0.7-1.0):</label>
                            <input type="number" id="temperature" name="temperature" value="0.8" min="0.7" max="1.0" step="0.1">
                        </div>
                        
                        <div class="form-group">
                            <label for="top_p">Top P (0.7-0.95):</label>
                            <input type="number" id="top_p" name="top_p" value="0.8" min="0.7" max="0.95" step="0.01">
                        </div>
                        
                        <button type="submit">🎵 Generate Speech</button>
                    </form>
                    
                    <div id="result" class="result" style="display: none;">
                        <h3>Result:</h3>
                        <div id="resultContent"></div>
                    </div>
                </div>
                
                <script>
                document.getElementById('ttsForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(this);
                    const params = new URLSearchParams(formData);
                    
                    document.getElementById('resultContent').innerHTML = 'Generating speech...';
                    document.getElementById('result').style.display = 'block';
                    
                    fetch('/synthesize?' + params.toString())
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                document.getElementById('resultContent').innerHTML = 
                                    '<p>✅ Speech generated successfully!</p>' +
                                    '<audio controls><source src="data:audio/wav;base64,' + data.audio + '" type="audio/wav"></audio>';
                            } else {
                                document.getElementById('resultContent').innerHTML = 
                                    '<p>❌ Error: ' + data.error + '</p>';
                            }
                        })
                        .catch(error => {
                            document.getElementById('resultContent').innerHTML = 
                                '<p>❌ Network error: ' + error + '</p>';
                        });
                });
                </script>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
            
        elif self.path.startswith('/synthesize'):
            self.handle_synthesize()
        else:
            self.send_response(404)
            self.end_headers()
    
    def handle_synthesize(self):
        """Handle speech synthesis requests"""
        try:
            # Parse query parameters
            parsed_url = urlparse(self.path)
            params = parse_qs(parsed_url.query)
            
            text = params.get('text', [''])[0]
            temperature = float(params.get('temperature', ['0.8'])[0])
            top_p = float(params.get('top_p', ['0.8'])[0])
            
            if not text:
                raise ValueError("Text parameter is required")
            
            logger.info(f"Synthesizing text: {text[:50]}...")
            
            # Create TTS request
            request = ServeTTSRequest(
                text=text,
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=top_p,
                repetition_penalty=1.1,
                temperature=temperature,
                format="wav",
            )
            
            # Generate audio
            audio_generator = inference_engine.inference(request)
            audio_data = b"".join(audio_generator)
            
            # Convert to base64 for web playback
            import base64
            audio_base64 = base64.b64encode(audio_data).decode()
            
            # Send response
            response = {
                "success": True,
                "audio": audio_base64,
                "text": text,
                "parameters": {
                    "temperature": temperature,
                    "top_p": top_p
                }
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            logger.info("✅ Speech synthesis completed successfully")
            
        except Exception as e:
            logger.error(f"❌ Synthesis error: {e}")
            
            response = {
                "success": False,
                "error": str(e)
            }
            
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

def parse_args():
    parser = ArgumentParser()
    parser.add_argument(
        "--llama-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini",
    )
    parser.add_argument(
        "--decoder-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini/codec.pth",
    )
    parser.add_argument("--decoder-config-name", type=str, default="modded_dac_vq")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--half", action="store_true")
    parser.add_argument("--compile", action="store_true")
    parser.add_argument("--port", type=int, default=8080, help="Port to run the API server on")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to run the API server on")

    return parser.parse_args()

if __name__ == "__main__":
    # Clear screen and show banner
    os.system('cls' if os.name == 'nt' else 'clear')
    big_text = pyfiglet.figlet_format("fish-speech50", font="slant")
    
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
{big_text.rstrip()}
╠══════════════════════════════════════════════════════════════════════════════╣
║                          API SERVER MODE                                    ║
║   📦 项目名称：fish-speech50                                                ║
║   🌐 运行模式：HTTP API 服务器（Web UI 替代方案）                           ║
║   📂 启动路径：{os.getcwd()}                                                 
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    
    print(banner)
    print("\n" + "═" * 80 + "\n")
    
    args = parse_args()
    args.precision = torch.half if args.half else torch.bfloat16

    # Check device availability
    if torch.backends.mps.is_available():
        args.device = "mps"
        logger.info("mps is available, running on mps.")
    elif torch.xpu.is_available():
        args.device = "xpu"
        logger.info("XPU is available, running on XPU.")
    elif not torch.cuda.is_available():
        logger.info("CUDA is not available, running on CPU.")
        args.device = "cpu"

    # Load models
    logger.info("Loading Llama model...")
    llama_queue = launch_thread_safe_queue(
        checkpoint_path=args.llama_checkpoint_path,
        device=args.device,
        precision=args.precision,
        compile=args.compile,
    )

    logger.info("Loading VQ-GAN model...")
    decoder_model = load_decoder_model(
        config_name=args.decoder_config_name,
        checkpoint_path=args.decoder_checkpoint_path,
        device=args.device,
    )

    logger.info("Decoder model loaded, warming up...")

    # Create inference engine
    inference_engine = TTSInferenceEngine(
        llama_queue=llama_queue,
        decoder_model=decoder_model,
        compile=args.compile,
        precision=args.precision,
    )

    # Warm up
    list(
        inference_engine.inference(
            ServeTTSRequest(
                text="Hello world.",
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=0.7,
                repetition_penalty=1.5,
                temperature=0.7,
                format="wav",
            )
        )
    )

    logger.info("Warming up done, starting API server...")
    
    # Start HTTP server
    try:
        server = HTTPServer((args.host, args.port), FishSpeechHandler)
        logger.info(f"🚀 API Server started successfully!")
        logger.info(f"🌐 Open your browser and go to: http://{args.host}:{args.port}")
        logger.info(f"📡 API endpoint: http://{args.host}:{args.port}/synthesize")
        logger.info("🛑 Press Ctrl+C to stop the server")
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
    finally:
        logger.info("👋 Goodbye!")

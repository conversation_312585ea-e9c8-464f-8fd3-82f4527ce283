import os
import time
import pyfiglet


# 清屏（可选）
os.system('cls' if os.name == 'nt' else 'clear')

# 标题大字
big_text = pyfiglet.figlet_format("fish-speech50", font="slant")

# 构建展示块
banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
{big_text.rstrip()}
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║   📦 项目名称：fish-speech50                                                ║
║   🧠 功能定位：多语音识别 + 多模型支持 + 图形交互                          ║
║                                                                              ║
║   👑 当前版本：v5.0   ｜  构建环境：PyTorch 2.8.0 + CUDA 12.8               ║
║   📂 启动路径：{os.getcwd()}                                                 
║                                                                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║   🎬 油管：王知风    ｜  📺 B站：AI王知风                                    ║
║   💬 AI工具QQ2群：773608333                                                ║
║   🧾 官网：wangzhifeng.vip   ｜ 作者：王知风                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""

print()
print(banner)
print("\n" + "═" * 80 + "\n")
time.sleep(1.5)
from argparse import ArgumentParser
from pathlib import Path

import pyrootutils
import torch
from loguru import logger

pyrootutils.setup_root(__file__, indicator=".project-root", pythonpath=True)

from fish_speech.inference_engine import TTSInferenceEngine
from fish_speech.models.dac.inference import load_model as load_decoder_model
from fish_speech.models.text2semantic.inference import launch_thread_safe_queue
from fish_speech.utils.schema import ServeTTSRequest
from tools.webui import build_app
from tools.webui.inference import get_inference_wrapper

# Make einx happy
os.environ["EINX_FILTER_TRACEBACK"] = "false"


def parse_args():
    parser = ArgumentParser()
    parser.add_argument(
        "--llama-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini",
    )
    parser.add_argument(
        "--decoder-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini/codec.pth",
    )
    parser.add_argument("--decoder-config-name", type=str, default="modded_dac_vq")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--half", action="store_true")
    parser.add_argument("--compile", action="store_true")
    parser.add_argument("--max-gradio-length", type=int, default=0)
    parser.add_argument("--theme", type=str, default="light")
    parser.add_argument("--share", action="store_true", help="Create a public shareable link")

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    args.precision = torch.half if args.half else torch.bfloat16

    # Check if MPS or CUDA is available
    if torch.backends.mps.is_available():
        args.device = "mps"
        logger.info("mps is available, running on mps.")
    elif torch.xpu.is_available():
        args.device = "xpu"
        logger.info("XPU is available, running on XPU.")
    elif not torch.cuda.is_available():
        logger.info("CUDA is not available, running on CPU.")
        args.device = "cpu"

    logger.info("Loading Llama model...")
    llama_queue = launch_thread_safe_queue(
        checkpoint_path=args.llama_checkpoint_path,
        device=args.device,
        precision=args.precision,
        compile=args.compile,
    )

    logger.info("Loading VQ-GAN model...")
    decoder_model = load_decoder_model(
        config_name=args.decoder_config_name,
        checkpoint_path=args.decoder_checkpoint_path,
        device=args.device,
    )

    logger.info("Decoder model loaded, warming up...")

    # Create the inference engine
    inference_engine = TTSInferenceEngine(
        llama_queue=llama_queue,
        decoder_model=decoder_model,
        compile=args.compile,
        precision=args.precision,
    )

    # Dry run to check if the model is loaded correctly and avoid the first-time latency
    list(
        inference_engine.inference(
            ServeTTSRequest(
                text="Hello world.",
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=0.7,
                repetition_penalty=1.5,
                temperature=0.7,
                format="wav",
            )
        )
    )

    logger.info("Warming up done, launching the web UI...")

    # Get the inference function with the immutable arguments
    inference_fct = get_inference_wrapper(inference_engine)

    app = build_app(inference_fct, theme="dark")
    
    # Launch with share=True to bypass localhost issues
    logger.info("🌐 Launching with public sharing enabled to bypass localhost issues...")
    logger.info("⚠️  This will create a temporary public URL that expires in 72 hours.")
    logger.info("🔒 Only share this URL with trusted users.")

    try:
        app.launch(
            share=True,  # Force sharing to bypass localhost issues
            inbrowser=True,
            show_api=True,
            quiet=False,
            show_error=True,
            debug=False,
            server_name=None,  # Let Gradio handle this
            server_port=None,  # Let Gradio choose port
            prevent_thread_lock=False
        )
        logger.info("✅ Web UI launched successfully with public sharing!")
    except Exception as e:
        logger.error(f"Failed to launch with sharing: {e}")
        logger.info("🔧 Trying alternative configuration...")

        # Try with explicit configuration
        try:
            import gradio as gr
            logger.info(f"Gradio version: {gr.__version__}")

            # Close any existing instances
            try:
                app.close()
            except:
                pass

            # Try launching with minimal configuration
            app.launch(
                share=True,
                inbrowser=False,  # Don't auto-open browser
                show_api=False,   # Disable API docs
                quiet=True,       # Reduce output
                show_error=True,
                debug=False,
                max_threads=10
            )
            logger.info("✅ Web UI launched successfully!")
            logger.info("🌐 A public URL has been generated. Check the output above for the link.")

        except Exception as e2:
            logger.error(f"All launch attempts failed: {e2}")
            logger.info("❌ Unable to start the web interface.")
            logger.info("")
            logger.info("🔧 Possible solutions:")
            logger.info("1. Check if your internet connection is working")
            logger.info("2. Try running as administrator")
            logger.info("3. Check Windows Firewall and antivirus settings")
            logger.info("4. Restart your computer and try again")
            logger.info("5. Try using a VPN if your network blocks certain connections")
            logger.info("")
            logger.info("📧 If the problem persists, contact support with this error message.")

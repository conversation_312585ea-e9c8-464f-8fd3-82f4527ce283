<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 7.2, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      Libswscale Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Libswscale Documentation
      </h1>


<a name="SEC_Top"></a>

<div class="region-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-See-Also" href="#See-Also">2 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">3 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>The libswscale library performs highly optimized image scaling and
colorspace and pixel format conversion operations.
</p>
<p>Specifically, this library performs the following conversions:
</p>
<ul class="itemize mark-bullet">
<li><em class="emph">Rescaling</em>: is the process of changing the video size. Several
rescaling options and algorithms are available. This is usually a
lossy process.

</li><li><em class="emph">Pixel format conversion</em>: is the process of converting the image
format and colorspace of the image, for example from planar YUV420P to
RGB24 packed. It also handles packing conversion, that is converts
from packed layout (all pixels belonging to distinct planes
interleaved in the same buffer), to planar layout (all samples
belonging to the same plane stored in a dedicated buffer or &quot;plane&quot;).

<p>This is usually a lossy process in case the source and destination
colorspaces differ.
</p></li></ul>


<a name="See-Also"></a>
<h2 class="chapter">2 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="ffmpeg-scaler.html">ffmpeg-scaler</a>,
<a class="url" href="libavutil.html">libavutil</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">3 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>

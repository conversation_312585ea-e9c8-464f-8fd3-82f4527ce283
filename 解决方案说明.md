# Fish Speech Web UI 启动问题解决方案

## 问题诊断

您遇到的问题是 **Gradio Web UI 无法在 localhost 上启动**，错误代码为 504。这是一个常见的网络配置问题，通常由以下原因引起：

1. **Windows 防火墙阻止本地连接**
2. **杀毒软件拦截网络连接**
3. **代理设置干扰**
4. **localhost 解析问题**

## 解决方案

### 方案 1：使用共享模式（推荐）

使用我创建的共享模式启动脚本：

```bash
启动程序_共享模式.bat
```

这个脚本会：
- 创建一个临时的公共 URL（72小时有效）
- 绕过 localhost 访问限制
- 自动打开浏览器

**优点：**
- 无需修改系统设置
- 绕过所有本地网络问题
- 立即可用

**注意事项：**
- 生成的 URL 是公开的，请勿分享给不信任的人
- URL 会在 72 小时后过期
- 需要互联网连接

### 方案 2：修复本地网络问题

如果您希望使用本地访问，请按以下步骤操作：

#### 步骤 1：检查防火墙设置

1. 打开 Windows 防火墙设置
2. 点击"允许应用通过防火墙"
3. 添加 Python 程序到例外列表：
   - 程序路径：`D:\ai\fish-speech50\wzf\python.exe`
   - 允许专用网络和公用网络访问

#### 步骤 2：检查杀毒软件

1. 临时禁用实时保护
2. 将项目文件夹添加到白名单：`D:\ai\fish-speech50`
3. 重新启动程序

#### 步骤 3：以管理员身份运行

1. 右键点击 `启动程序.bat`
2. 选择"以管理员身份运行"

#### 步骤 4：检查网络设置

运行网络诊断脚本：
```bash
网络诊断.bat
```

### 方案 3：使用修复版启动脚本

如果方案 1 和 2 都不行，使用修复版脚本：

```bash
启动程序_修复版.bat
```

这个脚本会：
- 尝试多个不同的端口
- 使用不同的网络配置
- 提供详细的错误信息

## 文件说明

我为您创建了以下文件来解决这个问题：

### 启动脚本

1. **`启动程序_共享模式.bat`** - 使用 Gradio 共享功能（推荐）
2. **`启动程序_修复版.bat`** - 尝试多种本地配置
3. **`tools/run_webui_share.py`** - 共享模式的 Python 脚本
4. **`tools/run_webui_simple.py`** - 简化版的 Python 脚本

### 诊断工具

1. **`网络诊断.bat`** - 检查网络和端口状态
2. **`test_gradio.py`** - 测试 Gradio 基本功能

### 修复的原始文件

1. **`tools/run_webui.py`** - 添加了错误处理和多端口尝试

## 使用建议

1. **首先尝试**：`启动程序_共享模式.bat`
2. **如果不想使用公共 URL**：按照方案 2 修复本地网络问题
3. **作为备选**：使用 `启动程序_修复版.bat`

## 技术细节

### 问题根因

Gradio 5.34.2 版本在某些 Windows 系统上存在 localhost 访问问题。错误 504 表示：
- Gradio 服务器启动成功
- 但无法访问 `http://127.0.0.1:7860/gradio_api/startup-events`
- 这通常是网络层面的阻止

### 共享模式工作原理

共享模式通过 Gradio 的云服务创建一个临时的公共隧道：
- 绕过本地网络限制
- 提供 `https://xxxxx.gradio.live` 格式的 URL
- 所有流量通过 Gradio 服务器中转

## 常见问题

**Q: 共享模式安全吗？**
A: 相对安全，但 URL 是公开的。不要处理敏感数据，不要分享 URL。

**Q: 为什么不能直接修复 localhost 问题？**
A: 这通常涉及系统级的网络配置，修改起来比较复杂且有风险。

**Q: 共享模式会影响性能吗？**
A: 会有轻微的网络延迟，但对于语音合成应用影响很小。

**Q: URL 过期后怎么办？**
A: 重新运行启动脚本即可获得新的 URL。

## 联系支持

如果所有方案都无法解决问题，请提供以下信息：
1. 运行 `网络诊断.bat` 的完整输出
2. Windows 版本和防火墙设置
3. 使用的杀毒软件
4. 网络环境（公司网络、家庭网络等）

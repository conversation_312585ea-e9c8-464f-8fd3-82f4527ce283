@echo off
:: Fish Speech Gradio Web UI Launcher - Environment Fix Version

:: Virtual environment path
set PYTHON_ENV=%cd%\wzf

:: Main program entry (using environment fix version)
set APP_ENTRY=.\tools\run_webui_env_fix.py

:: Gradio temp directory
set GRADIO_TEMP_DIR=%cd%\tmp

:: Clear interfering variables
set PYTHONHOME=
set PYTHONPATH=
set PYTHONEXECUTABLE=%PYTHON_ENV%\python.exe
set PYTHONWEXECUTABLE=%PYTHON_ENV%\pythonw.exe
set PYTHON_BIN_PATH=%PYTHONEXECUTABLE%
set PYTHON_LIB_PATH=%PYTHON_ENV%\Lib\site-packages

:: CUDA path
set CU_PATH=%PYTHON_ENV%\Lib\site-packages\torch\lib
set cuda_PATH=%PYTHON_ENV%\Library\bin

:: FFMPEG path
set FFMPEG_PATH=%PYTHON_ENV%\ffmpeg\bin

:: Update PATH
set PATH=%PYTHON_ENV%;%PYTHON_ENV%\Scripts;%FFMPEG_PATH%;%CU_PATH%;%cuda_PATH%;%PATH%

:: Set comprehensive Gradio environment variables to fix issues
set GRADIO_SERVER_NAME=0.0.0.0
set GRADIO_ANALYTICS_ENABLED=False
set GRADIO_DEBUG=False
set GRADIO_SHARE=False
set GRADIO_TEMP_DIR=%cd%\tmp
set GRADIO_FLAGGING_MODE=never
set GRADIO_ALLOW_FLAGGING=never

:: Network fix environment variables
set PYTHONHTTPSVERIFY=0
set CURL_CA_BUNDLE=
set REQUESTS_CA_BUNDLE=
set SSL_VERIFY=False

:: Create temp directory if it doesn't exist
if not exist "%cd%\tmp" mkdir "%cd%\tmp"

:: Check FFmpeg
echo Checking FFmpeg availability...
where ffmpeg

echo.
echo ========================================
echo Fish Speech Gradio Web UI - Env Fix
echo ========================================
echo.
echo This version uses environment variables to fix Gradio issues:
echo - Server address: 0.0.0.0 (instead of 127.0.0.1)
echo - Disabled analytics and debugging
echo - Disabled SSL verification
echo - Set proper temp directory
echo - Multiple fallback configurations
echo.
echo Starting Fish Speech Gradio Web UI...
echo.

"%PYTHONEXECUTABLE%" "%APP_ENTRY%"

echo.
echo Program ended, please check error messages above
pause

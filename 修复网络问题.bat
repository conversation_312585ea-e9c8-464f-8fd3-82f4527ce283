@echo off
:: Fish Speech Network Fix Script
:: This script attempts to fix common network issues that prevent Gradio from working

echo ========================================
echo Fish Speech Network Fix Tool
echo ========================================
echo.
echo This script will attempt to fix network issues preventing
echo Fish Speech Web UI from starting.
echo.
echo WARNING: This script requires Administrator privileges
echo and will modify system network settings.
echo.
pause

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ This script must be run as Administrator
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo.
echo 🔧 Starting network fixes...
echo.

echo 1. Resetting Windows network stack...
netsh winsock reset
netsh int ip reset
echo ✅ Network stack reset

echo.
echo 2. Flushing DNS cache...
ipconfig /flushdns
echo ✅ DNS cache flushed

echo.
echo 3. Releasing and renewing IP configuration...
ipconfig /release
ipconfig /renew
echo ✅ IP configuration renewed

echo.
echo 4. Checking localhost resolution...
ping -n 1 127.0.0.1 > nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Localhost (127.0.0.1) is reachable
) else (
    echo ❌ Localhost (127.0.0.1) is not reachable
    echo Adding localhost entry to hosts file...
    echo 127.0.0.1 localhost >> C:\Windows\System32\drivers\etc\hosts
    echo ✅ Localhost entry added
)

echo.
echo 5. Checking Windows Firewall status...
netsh advfirewall show allprofiles state | findstr "State"

echo.
echo 6. Adding Python to Windows Firewall exceptions...
set PYTHON_PATH=%cd%\wzf\python.exe
netsh advfirewall firewall add rule name="Fish Speech Python" dir=in action=allow program="%PYTHON_PATH%" enable=yes
netsh advfirewall firewall add rule name="Fish Speech Python" dir=out action=allow program="%PYTHON_PATH%" enable=yes
echo ✅ Python added to firewall exceptions

echo.
echo 7. Enabling loopback for Microsoft Store apps (if applicable)...
CheckNetIsolation LoopbackExempt -a -n="Microsoft.WindowsTerminal_8wekyb3d8bbwe"
echo ✅ Loopback exemption added

echo.
echo 8. Checking for proxy settings...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable 2>nul | findstr "0x1"
if %ERRORLEVEL% EQU 0 (
    echo ⚠️  Proxy is enabled - this might cause issues
    echo Consider temporarily disabling proxy in Internet Options
) else (
    echo ✅ No proxy detected
)

echo.
echo 9. Testing network connectivity...
ping -n 1 ******* > nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Internet connectivity working
) else (
    echo ❌ Internet connectivity issues detected
)

echo.
echo ========================================
echo Network Fix Complete
echo ========================================
echo.
echo The following changes have been made:
echo - Network stack reset
echo - DNS cache flushed
echo - IP configuration renewed
echo - Localhost entry verified/added
echo - Python added to firewall exceptions
echo - Loopback exemption added
echo.
echo IMPORTANT: You must restart your computer for all
echo changes to take effect.
echo.
echo After restart, try running Fish Speech again using:
echo 启动程序_强制共享.bat
echo.
echo Would you like to restart now? (Y/N)
set /p restart="Enter choice: "
if /i "%restart%"=="Y" (
    echo Restarting in 10 seconds...
    timeout /t 10
    shutdown /r /t 0
) else (
    echo Please restart manually when convenient.
)

pause

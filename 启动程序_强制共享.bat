@echo off
:: Fish Speech Web UI Launcher - Force Share Mode

:: Virtual environment path
set PYTHON_ENV=%cd%\wzf

:: Main program entry (using force share version)
set APP_ENTRY=.\tools\run_webui_force_share.py

:: Gradio temp directory
set GRADIO_TEMP_DIR=%cd%\tmp

:: Clear interfering variables
set PYTHONHOME=
set PYTHONPATH=
set PYTHONEXECUTABLE=%PYTHON_ENV%\python.exe
set PYTHONWEXECUTABLE=%PYTHON_ENV%\pythonw.exe
set PYTHON_BIN_PATH=%PYTHONEXECUTABLE%
set PYTHON_LIB_PATH=%PYTHON_ENV%\Lib\site-packages

:: CUDA path
set CU_PATH=%PYTHON_ENV%\Lib\site-packages\torch\lib
set cuda_PATH=%PYTHON_ENV%\Library\bin

:: FFMPEG path
set FFMPEG_PATH=%PYTHON_ENV%\ffmpeg\bin

:: Update PATH
set PATH=%PYTHON_ENV%;%PYTHON_ENV%\Scripts;%FFMPEG_PATH%;%CU_PATH%;%cuda_PATH%;%PATH%

:: Check FFmpeg
echo Checking FFmpeg availability...
where ffmpeg

echo.
echo ========================================
echo Fish Speech Web UI - Force Share Mode
echo ========================================
echo.
echo This version forces Gradio sharing to bypass localhost issues.
echo A public URL will be generated automatically.
echo.
echo IMPORTANT: This will create a temporary public URL that anyone
echo can access if they have the link. Do not share the URL with
echo untrusted users. The URL expires in 72 hours.
echo.
echo Starting Fish Speech Web UI...
echo.

"%PYTHONEXECUTABLE%" "%APP_ENTRY%"

echo.
echo Program ended, please check error messages above
pause

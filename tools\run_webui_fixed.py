import os
import time
import pyfiglet
import socket
import subprocess
import sys


# 清屏（可选）
os.system('cls' if os.name == 'nt' else 'clear')

# 标题大字
big_text = pyfiglet.figlet_format("fish-speech50", font="slant")

# 构建展示块
banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
{big_text.rstrip()}
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║   📦 项目名称：fish-speech50                                                ║
║   🧠 功能定位：多语音识别 + 多模型支持 + 图形交互                          ║
║                                                                              ║
║   👑 当前版本：v5.0   ｜  构建环境：PyTorch 2.8.0 + CUDA 12.8               ║
║   📂 启动路径：{os.getcwd()}                                                 
║                                                                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║   🎬 油管：王知风    ｜  📺 B站：AI王知风                                    ║
║   💬 AI工具QQ2群：773608333                                                ║
║   🧾 官网：wangzhifeng.vip   ｜ 作者：王知风                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""

print()
print(banner)
print("\n" + "═" * 80 + "\n")
time.sleep(1.5)

def fix_localhost_access():
    """修复 localhost 访问问题"""
    logger.info("🔧 正在修复 localhost 访问问题...")
    
    try:
        # 检查 hosts 文件
        hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
        localhost_entries = [
            "127.0.0.1 localhost",
            "::1 localhost"
        ]
        
        # 读取现有 hosts 文件
        try:
            with open(hosts_file, 'r', encoding='utf-8') as f:
                hosts_content = f.read()
        except:
            with open(hosts_file, 'r', encoding='gbk') as f:
                hosts_content = f.read()
        
        # 检查是否需要添加 localhost 条目
        needs_update = False
        for entry in localhost_entries:
            if entry not in hosts_content:
                hosts_content += f"\n{entry}"
                needs_update = True
        
        # 如果需要更新，写入 hosts 文件
        if needs_update:
            try:
                with open(hosts_file, 'w', encoding='utf-8') as f:
                    f.write(hosts_content)
                logger.info("✅ 已更新 hosts 文件")
            except PermissionError:
                logger.warning("⚠️ 无法写入 hosts 文件，请以管理员身份运行")
        
        # 刷新 DNS
        try:
            subprocess.run(['ipconfig', '/flushdns'], capture_output=True, check=True)
            logger.info("✅ 已刷新 DNS 缓存")
        except:
            logger.warning("⚠️ 无法刷新 DNS 缓存")
            
    except Exception as e:
        logger.warning(f"⚠️ localhost 修复过程中出现问题: {e}")

def find_available_port(start_port=7860, max_attempts=10):
    """查找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

from argparse import ArgumentParser
from pathlib import Path

import pyrootutils
import torch
from loguru import logger

pyrootutils.setup_root(__file__, indicator=".project-root", pythonpath=True)

from fish_speech.inference_engine import TTSInferenceEngine
from fish_speech.models.dac.inference import load_model as load_decoder_model
from fish_speech.models.text2semantic.inference import launch_thread_safe_queue
from fish_speech.utils.schema import ServeTTSRequest
from tools.webui import build_app
from tools.webui.inference import get_inference_wrapper

# Make einx happy
os.environ["EINX_FILTER_TRACEBACK"] = "false"

# 设置环境变量来修复 Gradio 问题
os.environ["GRADIO_SERVER_NAME"] = "127.0.0.1"
os.environ["GRADIO_ANALYTICS_ENABLED"] = "False"
os.environ["GRADIO_DEBUG"] = "False"


def parse_args():
    parser = ArgumentParser()
    parser.add_argument(
        "--llama-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini",
    )
    parser.add_argument(
        "--decoder-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini/codec.pth",
    )
    parser.add_argument("--decoder-config-name", type=str, default="modded_dac_vq")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--half", action="store_true")
    parser.add_argument("--compile", action="store_true")
    parser.add_argument("--max-gradio-length", type=int, default=0)
    parser.add_argument("--theme", type=str, default="light")
    parser.add_argument("--share", action="store_true", help="Create a public shareable link")
    parser.add_argument("--server-name", type=str, default="127.0.0.1", help="Server name")
    parser.add_argument("--server-port", type=int, default=None, help="Server port")

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    args.precision = torch.half if args.half else torch.bfloat16

    # 修复 localhost 访问
    fix_localhost_access()

    # Check if MPS or CUDA is available
    if torch.backends.mps.is_available():
        args.device = "mps"
        logger.info("mps is available, running on mps.")
    elif torch.xpu.is_available():
        args.device = "xpu"
        logger.info("XPU is available, running on XPU.")
    elif not torch.cuda.is_available():
        logger.info("CUDA is not available, running on CPU.")
        args.device = "cpu"

    logger.info("Loading Llama model...")
    llama_queue = launch_thread_safe_queue(
        checkpoint_path=args.llama_checkpoint_path,
        device=args.device,
        precision=args.precision,
        compile=args.compile,
    )

    logger.info("Loading VQ-GAN model...")
    decoder_model = load_decoder_model(
        config_name=args.decoder_config_name,
        checkpoint_path=args.decoder_checkpoint_path,
        device=args.device,
    )

    logger.info("Decoder model loaded, warming up...")

    # Create the inference engine
    inference_engine = TTSInferenceEngine(
        llama_queue=llama_queue,
        decoder_model=decoder_model,
        compile=args.compile,
        precision=args.precision,
    )

    # Dry run to check if the model is loaded correctly and avoid the first-time latency
    list(
        inference_engine.inference(
            ServeTTSRequest(
                text="Hello world.",
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=0.7,
                repetition_penalty=1.5,
                temperature=0.7,
                format="wav",
            )
        )
    )

    logger.info("Warming up done, launching the web UI...")

    # Get the inference function with the immutable arguments
    inference_fct = get_inference_wrapper(inference_engine)

    app = build_app(inference_fct, theme="dark")
    
    # 查找可用端口
    if args.server_port is None:
        available_port = find_available_port(7860)
        if available_port is None:
            logger.error("❌ 无法找到可用端口")
            sys.exit(1)
        args.server_port = available_port
    
    logger.info(f"🚀 准备在 {args.server_name}:{args.server_port} 启动 Gradio Web UI")
    
    # 尝试多种启动配置
    launch_configs = [
        # 配置 1: 标准本地启动
        {
            "server_name": args.server_name,
            "server_port": args.server_port,
            "share": False,
            "inbrowser": True,
            "show_api": True,
            "quiet": False,
            "show_error": True,
            "debug": False,
            "auth": None,
            "auth_message": None,
            "prevent_thread_lock": False,
            "height": 600,
            "width": None,
            "favicon_path": None,
            "ssl_keyfile": None,
            "ssl_certfile": None,
            "ssl_keyfile_password": None,
            "ssl_verify": True,
            "app_kwargs": {}
        },
        # 配置 2: 共享模式
        {
            "share": True,
            "inbrowser": True,
            "show_api": True,
            "quiet": False,
            "show_error": True,
            "debug": False,
            "prevent_thread_lock": False
        },
        # 配置 3: 最小配置
        {
            "server_name": "0.0.0.0",
            "server_port": args.server_port + 1,
            "share": False,
            "inbrowser": False,
            "show_api": False,
            "quiet": True,
            "show_error": True
        }
    ]
    
    for i, config in enumerate(launch_configs, 1):
        try:
            logger.info(f"🔄 尝试配置 {i}: {config.get('server_name', 'auto')}:{config.get('server_port', 'auto')}")
            
            app.launch(**config)
            
            # 如果到达这里，说明启动成功
            if config.get('share'):
                logger.info("✅ Gradio Web UI 已通过共享模式启动成功！")
                logger.info("🌐 请查看上方的公共 URL 链接")
            else:
                url = f"http://{config.get('server_name', '127.0.0.1')}:{config.get('server_port', args.server_port)}"
                logger.info(f"✅ Gradio Web UI 启动成功！")
                logger.info(f"🌐 请在浏览器中访问: {url}")
            
            break
            
        except Exception as e:
            logger.warning(f"⚠️ 配置 {i} 失败: {e}")
            
            # 如果不是最后一个配置，继续尝试
            if i < len(launch_configs):
                logger.info("🔄 尝试下一个配置...")
                try:
                    app.close()
                except:
                    pass
                continue
            else:
                # 所有配置都失败了
                logger.error("❌ 所有启动配置都失败了")
                logger.info("")
                logger.info("🔧 建议的解决方案:")
                logger.info("1. 以管理员身份运行此脚本")
                logger.info("2. 临时关闭防火墙和杀毒软件")
                logger.info("3. 检查网络代理设置")
                logger.info("4. 重启计算机后再试")
                logger.info("5. 使用 API 服务器替代方案: 启动API服务器.bat")
                break

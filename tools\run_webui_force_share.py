import os
import time
import pyfiglet


# 清屏（可选）
os.system('cls' if os.name == 'nt' else 'clear')

# 标题大字
big_text = pyfiglet.figlet_format("fish-speech50", font="slant")

# 构建展示块
banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
{big_text.rstrip()}
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║   📦 项目名称：fish-speech50                                                ║
║   🧠 功能定位：多语音识别 + 多模型支持 + 图形交互                          ║
║                                                                              ║
║   👑 当前版本：v5.0   ｜  构建环境：PyTorch 2.8.0 + CUDA 12.8               ║
║   📂 启动路径：{os.getcwd()}                                                 
║                                                                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║   🎬 油管：王知风    ｜  📺 B站：AI王知风                                    ║
║   💬 AI工具QQ2群：773608333                                                ║
║   🧾 官网：wangzhifeng.vip   ｜ 作者：王知风                                 ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""

print()
print(banner)
print("\n" + "═" * 80 + "\n")
time.sleep(1.5)
from argparse import ArgumentParser
from pathlib import Path

import pyrootutils
import torch
from loguru import logger

pyrootutils.setup_root(__file__, indicator=".project-root", pythonpath=True)

from fish_speech.inference_engine import TTSInferenceEngine
from fish_speech.models.dac.inference import load_model as load_decoder_model
from fish_speech.models.text2semantic.inference import launch_thread_safe_queue
from fish_speech.utils.schema import ServeTTSRequest
from tools.webui import build_app
from tools.webui.inference import get_inference_wrapper

# Make einx happy
os.environ["EINX_FILTER_TRACEBACK"] = "false"


def parse_args():
    parser = ArgumentParser()
    parser.add_argument(
        "--llama-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini",
    )
    parser.add_argument(
        "--decoder-checkpoint-path",
        type=Path,
        default="checkpoints/openaudio-s1-mini/codec.pth",
    )
    parser.add_argument("--decoder-config-name", type=str, default="modded_dac_vq")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--half", action="store_true")
    parser.add_argument("--compile", action="store_true")
    parser.add_argument("--max-gradio-length", type=int, default=0)
    parser.add_argument("--theme", type=str, default="light")

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()
    args.precision = torch.half if args.half else torch.bfloat16

    # Check if MPS or CUDA is available
    if torch.backends.mps.is_available():
        args.device = "mps"
        logger.info("mps is available, running on mps.")
    elif torch.xpu.is_available():
        args.device = "xpu"
        logger.info("XPU is available, running on XPU.")
    elif not torch.cuda.is_available():
        logger.info("CUDA is not available, running on CPU.")
        args.device = "cpu"

    logger.info("Loading Llama model...")
    llama_queue = launch_thread_safe_queue(
        checkpoint_path=args.llama_checkpoint_path,
        device=args.device,
        precision=args.precision,
        compile=args.compile,
    )

    logger.info("Loading VQ-GAN model...")
    decoder_model = load_decoder_model(
        config_name=args.decoder_config_name,
        checkpoint_path=args.decoder_checkpoint_path,
        device=args.device,
    )

    logger.info("Decoder model loaded, warming up...")

    # Create the inference engine
    inference_engine = TTSInferenceEngine(
        llama_queue=llama_queue,
        decoder_model=decoder_model,
        compile=args.compile,
        precision=args.precision,
    )

    # Dry run to check if the model is loaded correctly and avoid the first-time latency
    list(
        inference_engine.inference(
            ServeTTSRequest(
                text="Hello world.",
                references=[],
                reference_id=None,
                max_new_tokens=1024,
                chunk_length=200,
                top_p=0.7,
                repetition_penalty=1.5,
                temperature=0.7,
                format="wav",
            )
        )
    )

    logger.info("Warming up done, launching the web UI...")

    # Get the inference function with the immutable arguments
    inference_fct = get_inference_wrapper(inference_engine)

    app = build_app(inference_fct, theme="dark")
    
    # Force share mode - this is the only way to bypass localhost issues
    logger.info("🌐 Forcing share mode to bypass localhost restrictions...")
    logger.info("⚠️  A temporary public URL will be created (expires in 72 hours)")
    logger.info("🔒 Do not share this URL with untrusted users")
    logger.info("🚀 Starting Gradio with forced sharing...")
    
    try:
        # This should work even with localhost issues
        app.launch(
            share=True,           # Force sharing
            inbrowser=True,       # Try to open browser
            show_api=False,       # Disable API to reduce complexity
            quiet=False,          # Show output
            show_error=True,      # Show errors
            debug=False,          # Disable debug mode
            prevent_thread_lock=False,  # Allow blocking
            server_name=None,     # Let Gradio handle this
            server_port=None,     # Let Gradio choose port
            root_path=None,       # No custom root path
            app_kwargs=None,      # No custom app kwargs
            state_session_capacity=10000,  # Default capacity
            max_threads=40        # Default thread count
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to launch with forced sharing: {e}")
        logger.info("")
        logger.info("🔧 This appears to be a fundamental network connectivity issue.")
        logger.info("📋 Please try the following solutions:")
        logger.info("")
        logger.info("1. 🛡️  Temporarily disable Windows Firewall:")
        logger.info("   - Open Windows Security")
        logger.info("   - Go to Firewall & network protection")
        logger.info("   - Turn off firewall for private networks")
        logger.info("   - Try running the program again")
        logger.info("   - Remember to turn firewall back on afterwards")
        logger.info("")
        logger.info("2. 🦠 Temporarily disable antivirus software:")
        logger.info("   - Disable real-time protection")
        logger.info("   - Add the project folder to exclusions")
        logger.info("   - Try running the program again")
        logger.info("")
        logger.info("3. 👑 Run as Administrator:")
        logger.info("   - Right-click on the .bat file")
        logger.info("   - Select 'Run as administrator'")
        logger.info("")
        logger.info("4. 🌐 Check internet connection:")
        logger.info("   - Ensure you have a stable internet connection")
        logger.info("   - Try using a different network (mobile hotspot)")
        logger.info("")
        logger.info("5. 🔄 System restart:")
        logger.info("   - Restart your computer")
        logger.info("   - Try running the program again")
        logger.info("")
        logger.info("6. 🏢 Corporate/School network:")
        logger.info("   - If on a corporate or school network, contact IT")
        logger.info("   - These networks often block certain connections")
        logger.info("")
        logger.info("💡 The models loaded successfully, so the issue is purely network-related.")
        logger.info("📧 If none of these solutions work, this may require system-level network troubleshooting.")
        
    input("\nPress Enter to exit...")

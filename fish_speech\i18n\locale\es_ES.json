{"16-mixed is recommended for 10+ series GPU": "se recomienda 16-mixed para GPU de la serie 10+", "5 to 10 seconds of reference audio, useful for specifying speaker.": "5 a 10 segundos de audio de referencia, útil para especificar el hablante.", "A text-to-speech model based on VQ-GAN and Llama developed by [Fish Audio](https://fish.audio).": "Un modelo de texto a voz basado en VQ-GAN y Llama desarrollado por [Fish Audio](https://fish.audio).", "Accumulate Gradient Batches": "Acumular lotes de gradientes", "Add to Processing Area": "Agregar al Área de Procesamiento", "Added path successfully!": "¡Ruta agregada exitosamente!", "Advanced Config": "Configuración Avanzada", "Base LLAMA Model": "Modelo Base LLAMA", "Batch Inference": "Inferencia por Lote", "Batch Size": "Tamaño del Lote", "Changing with the Model Path": "Cambiando con la Ruta del Modelo", "Chinese": "Chino", "Compile Model": "Compilar <PERSON>", "Compile the model can significantly reduce the inference time, but will increase cold start time": "Compilar el modelo puede reducir significativamente el tiempo de inferencia, pero aumentará el tiempo de inicio en frío", "Copy": "Copiar", "Data Preprocessing": "Preprocesamiento de Datos", "Data Preprocessing Path": "Ruta de Preprocesamiento de Datos", "Data Source": "Fuente de Datos", "Decoder Model Config": "Configuración del modelo decodificador", "Decoder Model Path": "Ruta del modelo decodificador", "Disabled": "Desactivado", "Enable Reference Audio": "Habilitar Audio de Referencia", "English": "Inglés", "Error Message": "<PERSON><PERSON><PERSON>", "File Preprocessing": "Preprocesamiento de Archivos", "Generate": "Generar", "Generated Audio": "Audio Generado", "If there is no corresponding text for the audio, apply ASR for assistance, support .txt or .lab format": "Si no hay texto correspondiente para el audio, aplique ASR para asistencia, soporte para formato .txt o .lab", "Infer interface is closed": "La interfaz de inferencia está cerrada", "Inference Configuration": "Configuración de Inferencia", "Inference Server Configuration": "Configuración del Servidor de Inferencia", "Inference Server Error": "Error del Servidor de Inferencia", "Inferring interface is launched at {}": "La interfaz de inferencia se ha lanzado en {}", "Initial Learning Rate": "Tasa de Aprendizaje Inicial", "Input Audio & Source Path for Transcription": "Audio de Entrada y Ruta de Origen para Transcripción", "Input Text": "Texto de Entrada", "Invalid path: {}": "<PERSON><PERSON> inv<PERSON>lida: {}", "It is recommended to use CUDA, if you have low configuration, use CPU": "Se recomienda usar CUDA, si tiene una configuración baja, use CPU", "Iterative Prompt Length, 0 means off": "Longitud de la Indicación Iterativa, 0 significa apagado", "Japanese": "Japonés", "LLAMA Configuration": "Configuración de LLAMA", "LLAMA Model Config": "Configuración del Modelo LLAMA", "LLAMA Model Path": "Ruta del Modelo LLAMA", "Labeling Device": "Dispositivo de Etiquetado", "LoRA Model to be merged": "Modelo LoRA a fusionar", "Maximum Audio Duration": "Duración máxima de audio", "Maximum Length per Sample": "<PERSON><PERSON><PERSON> p<PERSON>", "Maximum Training Steps": "Pasos Máximos de Entrenamiento", "Maximum tokens per batch, 0 means no limit": "Máximo de tokens por lote, 0 significa sin límite", "Merge": "Fusionar", "Merge LoRA": "Fusionar LoRA", "Merge successfully": "Fusionado exitosamente", "Minimum Audio Duration": "Duración mínima de audio", "Model Output Path": "Ruta de Salida del Modelo", "Model Size": "Tamaño del Modelo", "Move": "Mover", "Move files successfully": "Archivos movidos exitosamente", "No audio generated, please check the input text.": "No se generó audio, por favor verifique el texto de entrada.", "No selected options": "No hay opciones seleccionadas", "Number of Workers": "Número de Trabajadores", "Open Inference Server": "<PERSON><PERSON><PERSON> Servidor de Inferencia", "Open Labeler WebUI": "Abrir Interfaz Web del Etiquetador", "Open Tensorboard": "Abrir Tensorboard", "Opened labeler in browser": "Se abrió el etiquetador en el navegador", "Optional Label Language": "Idioma de Etiquetado Opcional", "Optional online ver": "Ver en línea opcional", "Output Path": "Ruta de Salida", "Path error, please check the model file exists in the corresponding path": "Error de ruta, por favor verifique que el archivo del modelo exista en la ruta correspondiente", "Precision": "Precisión", "Probability of applying Speaker Condition": "Probabilidad de aplicar Condición de Hablante", "Put your text here.": "Ponga su texto aquí.", "Reference Audio": "Audio de Referencia", "Reference Text": "Texto de Referencia", "Related code and weights are released under CC BY-NC-SA 4.0 License.": "El código relacionado y los pesos se publican bajo la Licencia CC BY-NC-SA 4.0.", "Remove Selected Data": "Eliminar Da<PERSON>", "Removed path successfully!": "¡Ruta eliminada exitosamente!", "Repetition Penalty": "Penalización por Repetición", "Save model every n steps": "Guardar modelo cada n pasos", "Select LLAMA ckpt": "Seleccionar punto de control LLAMA", "Select VITS ckpt": "Seleccionar punto de control VITS", "Select VQGAN ckpt": "Seleccionar punto de control VQGAN", "Select source file processing method": "Seleccione el método de procesamiento de archivos fuente", "Select the model to be trained (Depending on the Tab page you are on)": "Seleccione el modelo a entrenar (Dependiendo de la pestaña en la que se encuentre)", "Selected: {}": "Seleccionado: {}", "Speaker": "<PERSON><PERSON><PERSON>", "Speaker is identified by the folder name": "El hablante se identifica por el nombre de la carpeta", "Start Training": "Iniciar <PERSON>", "Streaming Audio": "transmisión de audio", "Streaming Generate": "síntesis en flujo", "Tensorboard Host": "Host de Tensorboard", "Tensorboard Log Path": "Ruta de Registro de Tensorboard", "Tensorboard Port": "Puerto de Tensorboard", "Tensorboard interface is closed": "La interfaz de Tensorboard está cerrada", "Tensorboard interface is launched at {}": "La interfaz de Tensorboard se ha lanzado en {}", "Text is too long, please keep it under {} characters.": "El texto es demasiado largo, por favor manténgalo por debajo de {} caracteres.", "The path of the input folder on the left or the filelist. Whether checked or not, it will be used for subsequent training in this list.": "La ruta de la carpeta de entrada a la izquierda o la lista de archivos. Ya sea que esté marcado o no, se utilizará para el entrenamiento posterior en esta lista.", "Training Configuration": "Configuración de Entrenamiento", "Training Error": "Error de Entrenamiento", "Training stopped": "Entrenamiento detenido", "Type name of the speaker": "Escriba el nombre del hablante", "Type the path or select from the dropdown": "Escriba la ruta o seleccione de la lista desplegable", "Use LoRA": "Usar <PERSON>", "Use LoRA can save GPU memory, but may reduce the quality of the model": "Usar LoRA puede ahorrar memoria GPU, pero puede reducir la calidad del modelo", "Use filelist": "Usar lista de archivos", "Use large for 10G+ GPU, medium for 5G, small for 2G": "Use grande para GPU de 10G+, mediano para 5G, pequeño para 2G", "VITS Configuration": "Configuración de VITS", "VQGAN Configuration": "Configuración de VQGAN", "Validation Batch Size": "Tamaño del Lote de Validación", "View the status of the preprocessing folder (use the slider to control the depth of the tree)": "Vea el estado de la carpeta de preprocesamiento (use el control deslizante para controlar la profundidad del árbol)", "We are not responsible for any misuse of the model, please consider your local laws and regulations before using it.": "No somos responsables de ningún mal uso del modelo, por favor considere sus leyes y regulaciones locales antes de usarlo.", "WebUI Host": "Host de WebUI", "WebUI Port": "Puerto de WebUI", "Whisper Model": "<PERSON><PERSON>", "You can find the source code [here](https://github.com/fishaudio/fish-speech) and models [here](https://huggingface.co/fishaudio/fish-speech-1).": "Puede encontrar el código fuente [aquí](https://github.com/fishaudio/fish-speech) y los modelos [aquí](https://huggingface.co/fishaudio/fish-speech-1).", "bf16-true is recommended for 30+ series GPU, 16-mixed is recommended for 10+ series GPU": "Se recomienda bf16-true para GPU de la serie 30+, se recomienda 16-mixed para GPU de la serie 10+", "latest": "más reciente", "new": "nuevo", "Realtime Transform Text": "Transformación de Texto en Tiempo Real", "Normalization Result Preview (Currently Only Chinese)": "Vista Previa del Resultado de Normalización (Actualmente Solo Chino)", "Text Normalization": "Normalización de Texto", "Select Example Audio": "Selecionar áudio de exemplo"}
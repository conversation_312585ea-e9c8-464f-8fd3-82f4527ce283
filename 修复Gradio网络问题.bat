@echo off
:: Gradio Network Fix Script - Specifically for Fish Speech Web UI

echo ========================================
echo Gradio Network Fix Tool for Fish Speech
echo ========================================
echo.
echo This script will fix common network issues that prevent
echo Gradio Web UI from starting properly.
echo.
echo WARNING: This script requires Administrator privileges
echo and will modify system network settings.
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ❌ This script must be run as Administrator
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo.
echo 🔧 Starting Gradio-specific network fixes...
echo.

echo 1. Fixing localhost resolution...
echo.
:: Check and fix hosts file
set HOSTS_FILE=C:\Windows\System32\drivers\etc\hosts

:: Backup hosts file
copy "%HOSTS_FILE%" "%HOSTS_FILE%.backup" >nul 2>&1

:: Check if localhost entries exist
findstr /C:"127.0.0.1 localhost" "%HOSTS_FILE%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo Adding 127.0.0.1 localhost entry...
    echo 127.0.0.1 localhost >> "%HOSTS_FILE%"
)

findstr /C:"::1 localhost" "%HOSTS_FILE%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo Adding ::1 localhost entry...
    echo ::1 localhost >> "%HOSTS_FILE%"
)

:: Add specific Gradio entries
findstr /C:"127.0.0.1 gradio.local" "%HOSTS_FILE%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 127.0.0.1 gradio.local >> "%HOSTS_FILE%"
)

echo ✅ Localhost entries verified/added

echo.
echo 2. Resetting network stack...
netsh winsock reset >nul 2>&1
netsh int ip reset >nul 2>&1
echo ✅ Network stack reset

echo.
echo 3. Flushing DNS cache...
ipconfig /flushdns >nul 2>&1
echo ✅ DNS cache flushed

echo.
echo 4. Renewing IP configuration...
ipconfig /release >nul 2>&1
ipconfig /renew >nul 2>&1
echo ✅ IP configuration renewed

echo.
echo 5. Testing localhost connectivity...
ping -n 1 127.0.0.1 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Localhost (127.0.0.1) is reachable
) else (
    echo ❌ Localhost still not reachable - may need system restart
)

echo.
echo 6. Configuring Windows Firewall for Python...
set PYTHON_PATH=%cd%\wzf\python.exe

:: Remove existing rules (ignore errors)
netsh advfirewall firewall delete rule name="Fish Speech Python" >nul 2>&1

:: Add new rules
netsh advfirewall firewall add rule name="Fish Speech Python In" dir=in action=allow program="%PYTHON_PATH%" enable=yes >nul 2>&1
netsh advfirewall firewall add rule name="Fish Speech Python Out" dir=out action=allow program="%PYTHON_PATH%" enable=yes >nul 2>&1

:: Add port-specific rules for common Gradio ports
for %%p in (7860 7861 7862 7863 8080) do (
    netsh advfirewall firewall add rule name="Gradio Port %%p" dir=in action=allow protocol=TCP localport=%%p >nul 2>&1
)

echo ✅ Firewall rules configured

echo.
echo 7. Disabling IPv6 (temporary fix for some Gradio issues)...
netsh interface ipv6 set global randomizeidentifiers=disabled >nul 2>&1
netsh interface ipv6 set privacy state=disabled >nul 2>&1
echo ✅ IPv6 configuration adjusted

echo.
echo 8. Setting up loopback exemptions...
CheckNetIsolation LoopbackExempt -a -n="Microsoft.WindowsTerminal_8wekyb3d8bbwe" >nul 2>&1
CheckNetIsolation LoopbackExempt -a -n="Microsoft.Windows.Cortana_cw5n1h2txyewy" >nul 2>&1
echo ✅ Loopback exemptions added

echo.
echo 9. Checking and fixing proxy settings...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable 2>nul | findstr "0x1" >nul
if %ERRORLEVEL% EQU 0 (
    echo ⚠️ Proxy is enabled - this may cause Gradio issues
    echo Temporarily disabling proxy...
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f >nul 2>&1
    echo ✅ Proxy temporarily disabled
) else (
    echo ✅ No proxy detected
)

echo.
echo 10. Creating Gradio temp directory...
if not exist "%cd%\tmp" mkdir "%cd%\tmp"
echo ✅ Temp directory ready

echo.
echo 11. Setting optimal network parameters...
:: Increase TCP connection limits
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
echo ✅ Network parameters optimized

echo.
echo ========================================
echo Gradio Network Fix Complete
echo ========================================
echo.
echo The following changes have been made:
echo ✅ Localhost resolution fixed
echo ✅ Network stack reset
echo ✅ DNS cache flushed
echo ✅ IP configuration renewed
echo ✅ Firewall rules added for Python and Gradio ports
echo ✅ IPv6 configuration adjusted
echo ✅ Loopback exemptions added
echo ✅ Proxy temporarily disabled (if was enabled)
echo ✅ Gradio temp directory created
echo ✅ Network parameters optimized
echo.
echo IMPORTANT: You should restart your computer for all
echo changes to take effect properly.
echo.
echo After restart, try running Fish Speech Web UI using:
echo 启动WebUI_修复版.bat
echo.
set /p restart="Would you like to restart now? (Y/N): "
if /i "%restart%"=="Y" (
    echo Restarting in 10 seconds...
    echo Press Ctrl+C to cancel...
    timeout /t 10
    shutdown /r /t 0
) else (
    echo.
    echo Please restart manually when convenient.
    echo Then run: 启动WebUI_修复版.bat
)

pause
